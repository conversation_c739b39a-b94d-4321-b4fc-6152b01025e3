package com.dwdo.hotdesk.dto;

import com.dwdo.hotdesk.model.Submission;
import com.dwdo.hotdesk.service.SubmissionDetailService;
import com.dwdo.hotdesk.service.SubmissionHistoryService;
import com.dwdo.hotdesk.service.TaskListService;
import com.dwdo.hotdesk.util.NullUtil;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.test.util.ReflectionTestUtils;

import java.math.BigDecimal;
import java.time.LocalDate;

import static org.assertj.core.api.Assertions.assertThat;

@DisplayName("Payment Date and Remarks Mapping Tests")
class PaymentDateRemarksTest {

    @Test
    @DisplayName("Should map payment date and remarks correctly in SubmissionDetailDTO")
    void testSubmissionDetailDTO_PaymentDateAndRemarks() {
        // Given
        Submission submission = createSubmissionWithPaymentDateAndRemarks();
        SubmissionDetailService service = new SubmissionDetailService(null, null);
        
        // When - Use reflection to call private mapping method
        SubmissionDetailDTO result = (SubmissionDetailDTO) ReflectionTestUtils.invokeMethod(
                service, "mapToDTO", submission);
        
        // Then
        assertThat(result).isNotNull();
        assertThat(result.getPaymentDate()).isEqualTo("2024-01-15");
        assertThat(result.getRemarks()).isEqualTo("Test remarks");
    }

    @Test
    @DisplayName("Should display '-' for null/empty remarks in SubmissionDetailDTO")
    void testSubmissionDetailDTO_NullRemarks() {
        // Given
        Submission submission = createSubmissionWithPaymentDateAndRemarks();
        submission.setRemarks(null);
        SubmissionDetailService service = new SubmissionDetailService(null, null);
        
        // When
        SubmissionDetailDTO result = (SubmissionDetailDTO) ReflectionTestUtils.invokeMethod(
                service, "mapToDTO", submission);
        
        // Then
        assertThat(result).isNotNull();
        assertThat(result.getPaymentDate()).isEqualTo("2024-01-15");
        assertThat(result.getRemarks()).isEqualTo("-");
    }

    @Test
    @DisplayName("Should display '-' for empty remarks in SubmissionDetailDTO")
    void testSubmissionDetailDTO_EmptyRemarks() {
        // Given
        Submission submission = createSubmissionWithPaymentDateAndRemarks();
        submission.setRemarks("   ");
        SubmissionDetailService service = new SubmissionDetailService(null, null);
        
        // When
        SubmissionDetailDTO result = (SubmissionDetailDTO) ReflectionTestUtils.invokeMethod(
                service, "mapToDTO", submission);
        
        // Then
        assertThat(result).isNotNull();
        assertThat(result.getPaymentDate()).isEqualTo("2024-01-15");
        assertThat(result.getRemarks()).isEqualTo("-");
    }

    @Test
    @DisplayName("Should map payment date and remarks correctly in SubmissionHistoryDTO")
    void testSubmissionHistoryDTO_PaymentDateAndRemarks() {
        // Given
        Submission submission = createSubmissionWithPaymentDateAndRemarks();
        SubmissionHistoryService service = new SubmissionHistoryService(null, null);

        // When
        SubmissionHistoryDTO result = (SubmissionHistoryDTO) ReflectionTestUtils.invokeMethod(
                service, "mapToDTO", submission);

        // Then
        assertThat(result).isNotNull();
        assertThat(result.getPaymentDate()).isEqualTo("2024-01-15");
        assertThat(result.getRemarks()).isEqualTo("Test remarks");
    }

    @Test
    @DisplayName("Should display '-' for null payment date in SubmissionDetailDTO")
    void testSubmissionDetailDTO_NullPaymentDate() {
        // Given
        Submission submission = createSubmissionWithPaymentDateAndRemarks();
        submission.setPaymentDate(null);
        SubmissionDetailService service = new SubmissionDetailService(null, null);

        // When
        SubmissionDetailDTO result = (SubmissionDetailDTO) ReflectionTestUtils.invokeMethod(
                service, "mapToDTO", submission);

        // Then
        assertThat(result).isNotNull();
        assertThat(result.getPaymentDate()).isEqualTo("-");
        assertThat(result.getRemarks()).isEqualTo("Test remarks");
    }

    @Test
    @DisplayName("Should map payment date and remarks correctly in TaskListDTO")
    void testTaskListDTO_PaymentDateAndRemarks() {
        // Given
        Submission submission = createSubmissionWithPaymentDateAndRemarks();
        TaskListService service = new TaskListService(null, null);
        
        // When
        TaskListDTO result = (TaskListDTO) ReflectionTestUtils.invokeMethod(
                service, "mapToTaskListDTO", submission);
        
        // Then
        assertThat(result).isNotNull();
        assertThat(result.getPaymentDate()).isEqualTo("2024-01-15");
        assertThat(result.getRemarks()).isEqualTo("Test remarks");
    }

    @Test
    @DisplayName("Should demonstrate NullUtil usage for payment date and remarks")
    void testNullUtilUsage() {
        // Given
        Submission submissionWithNulls = Submission.builder()
                .paymentDate(null)
                .remarks(null)
                .build();

        Submission submissionWithValues = Submission.builder()
                .paymentDate(LocalDate.of(2024, 1, 15))
                .remarks("Test remarks")
                .build();

        // When & Then - Test null values
        assertThat(NullUtil.toDisplayString(submissionWithNulls.getPaymentDate())).isEqualTo("-");
        assertThat(NullUtil.toDisplayString(submissionWithNulls.getRemarks())).isEqualTo("-");

        // When & Then - Test valid values
        assertThat(NullUtil.toDisplayString(submissionWithValues.getPaymentDate())).isEqualTo("2024-01-15");
        assertThat(NullUtil.toDisplayString(submissionWithValues.getRemarks())).isEqualTo("Test remarks");

        // When & Then - Test empty string
        assertThat(NullUtil.toDisplayString("")).isEqualTo("-");
        assertThat(NullUtil.toDisplayString("   ")).isEqualTo("-");
    }

    private Submission createSubmissionWithPaymentDateAndRemarks() {
        return Submission.builder()
                .id(1L)
                .taskName("TASK-001")
                .referenceNumber("REF-001")
                .submitterName("John Doe")
                .submitterJob("Developer")
                .status("PENDING")
                .nip("123456789")
                .name("John Doe")
                .grade("A")
                .paymentType("Salary")
                .amount(new BigDecimal("5000000"))
                .description("Monthly salary")
                .monthOfProcess("January")
                .yearOfProcess("2024")
                .directorate("IT")
                .slik("-")
                .sanction("-")
                .terminationDate("-")
                .eligible(true)
                .currentReviewer("<EMAIL>")
                .paymentDate(LocalDate.of(2024, 1, 15))
                .remarks("Test remarks")
                .build();
    }
}
