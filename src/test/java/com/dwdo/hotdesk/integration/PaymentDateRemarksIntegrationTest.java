package com.dwdo.hotdesk.integration;

import com.dwdo.hotdesk.dto.SubmissionDetailDTO;
import com.dwdo.hotdesk.dto.SubmissionHistoryDTO;
import com.dwdo.hotdesk.dto.TaskListDTO;
import com.dwdo.hotdesk.model.Submission;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import java.math.BigDecimal;
import java.time.LocalDate;

import static org.assertj.core.api.Assertions.assertThat;

@DisplayName("Payment Date and Remarks Integration Tests")
class PaymentDateRemarksIntegrationTest {

    @Test
    @DisplayName("Should verify DTO fields are correctly defined")
    void testDTOFieldsExist() {
        // Test SubmissionDetailDTO
        SubmissionDetailDTO detailDTO = SubmissionDetailDTO.builder()
                .paymentDate("2024-01-15")
                .remarks("Test remarks")
                .build();
        
        assertThat(detailDTO.getPaymentDate()).isEqualTo("2024-01-15");
        assertThat(detailDTO.getRemarks()).isEqualTo("Test remarks");

        // Test SubmissionHistoryDTO
        SubmissionHistoryDTO historyDTO = SubmissionHistoryDTO.builder()
                .paymentDate("2024-01-15")
                .remarks("Test remarks")
                .build();
        
        assertThat(historyDTO.getPaymentDate()).isEqualTo("2024-01-15");
        assertThat(historyDTO.getRemarks()).isEqualTo("Test remarks");

        // Test TaskListDTO
        TaskListDTO taskListDTO = TaskListDTO.builder()
                .paymentDate("2024-01-15")
                .remarks("Test remarks")
                .build();
        
        assertThat(taskListDTO.getPaymentDate()).isEqualTo("2024-01-15");
        assertThat(taskListDTO.getRemarks()).isEqualTo("Test remarks");
    }

    @Test
    @DisplayName("Should verify Submission entity has payment date and remarks fields")
    void testSubmissionEntityFields() {
        // Given
        Submission submission = Submission.builder()
                .id(1L)
                .taskName("TASK-001")
                .referenceNumber("REF-001")
                .submitterName("John Doe")
                .submitterJob("Developer")
                .status("PENDING")
                .nip("123456789")
                .name("John Doe")
                .grade("A")
                .paymentType("Salary")
                .amount(new BigDecimal("5000000"))
                .description("Monthly salary")
                .monthOfProcess("January")
                .yearOfProcess("2024")
                .directorate("IT")
                .eligible(true)
                .currentReviewer("<EMAIL>")
                .paymentDate(LocalDate.of(2024, 1, 15))
                .remarks("Test remarks")
                .build();

        // Then
        assertThat(submission.getPaymentDate()).isEqualTo(LocalDate.of(2024, 1, 15));
        assertThat(submission.getRemarks()).isEqualTo("Test remarks");
    }

    @Test
    @DisplayName("Should handle null values correctly")
    void testNullValues() {
        // Given
        Submission submission = Submission.builder()
                .id(1L)
                .taskName("TASK-001")
                .referenceNumber("REF-001")
                .submitterName("John Doe")
                .submitterJob("Developer")
                .status("PENDING")
                .nip("123456789")
                .name("John Doe")
                .grade("A")
                .paymentType("Salary")
                .amount(new BigDecimal("5000000"))
                .description("Monthly salary")
                .monthOfProcess("January")
                .yearOfProcess("2024")
                .directorate("IT")
                .eligible(true)
                .currentReviewer("<EMAIL>")
                .paymentDate(null)  // null payment date
                .remarks(null)      // null remarks
                .build();

        // Then
        assertThat(submission.getPaymentDate()).isNull();
        assertThat(submission.getRemarks()).isNull();
    }
}
