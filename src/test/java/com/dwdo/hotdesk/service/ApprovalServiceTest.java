package com.dwdo.hotdesk.service;

import com.dwdo.hotdesk.model.Submission;
import com.dwdo.hotdesk.repository.PaymentSubmissionRepository;
import com.dwdo.hotdesk.service.feign.ApprovalClient;
import com.dwdo.hotdesk.service.feign.response.ApiResponse;
import com.dwdo.hotdesk.service.feign.response.DetailResponse;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.test.util.ReflectionTestUtils;

import java.math.BigDecimal;
import java.util.Arrays;
import java.util.List;
import java.util.Optional;

import java.util.Optional;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
@DisplayName("Approval Service Tests")
class ApprovalServiceTest {

    @Mock
    private PaymentSubmissionRepository submissionRepository;

    @Mock
    private ApprovalClient approvalClient;

    @Mock
    private ApprovalService service;

    @InjectMocks
    private ApprovalService approvalService;

    private Submission mockSubmission;
    private static final String TASK_NAME = "TEST_TASK_123";
    private static final String ACTION = "APPROVE";
    private static final String REASON = "Test reason";

    @BeforeEach
    void setUp() {
        mockSubmission = new Submission();
        mockSubmission.setId(1L);
        mockSubmission.setTaskName(TASK_NAME);
        mockSubmission.setStatus("Waiting for Approval");
        mockSubmission.setNip("123456");
        mockSubmission.setName("Test User");
    }

    @Test
    @DisplayName("Should set status to 'Waiting for Payment' when statusCode is 'complete' and clear reviewer")
    void testUpdateCurrentReviewer_CompleteStatus() throws Exception {
        // Given
        mockSubmission.setStatus("Pending"); // Start with Pending status
        mockSubmission.setCurrentReviewer("previous reviewer data"); // Set initial reviewer data

        DetailResponse detailResponse = new DetailResponse();
        detailResponse.setStatusCode("complete");
        detailResponse.setStatus("Task Is In Completed");

        ApiResponse apiResponse = new ApiResponse();
        apiResponse.setSuccess(true);
        apiResponse.setData(detailResponse);

        when(submissionRepository.findByTaskName(TASK_NAME)).thenReturn(Optional.of(mockSubmission));
        when(approvalClient.detail(TASK_NAME)).thenReturn(apiResponse);

        // When
        ReflectionTestUtils.invokeMethod(approvalService, "updateCurrentReviewer", TASK_NAME, ACTION, REASON);

        // Then
        verify(submissionRepository).findByTaskName(TASK_NAME);
        verify(approvalClient).detail(TASK_NAME); // Called once in updateSubmissionStatusByAction
        verify(submissionRepository).save(mockSubmission);

        // Verify that the submission status was updated to "Waiting for Payment" and reviewer was cleared
        assert mockSubmission.getStatus().equals("Waiting for Payment");
        assert mockSubmission.getRemarks().equals(REASON);
        assert mockSubmission.getCurrentReviewer().equals(""); // Reviewer should be cleared for completed transactions
    }

    @Test
    @DisplayName("Should set status to 'Waiting for Approval' when statusCode is not 'complete' and update reviewer")
    void testUpdateCurrentReviewer_NonCompleteStatus() throws Exception {
        // Given
        mockSubmission.setStatus("Pending"); // Start with Pending status

        DetailResponse detailResponse = new DetailResponse();
        detailResponse.setStatusCode("pending");
        detailResponse.setStatus("Task Is Pending");

        ApiResponse apiResponse = new ApiResponse();
        apiResponse.setSuccess(true);
        apiResponse.setData(detailResponse);

        ApiResponse detailApiResponse = new ApiResponse();
        detailApiResponse.setSuccess(true);
        detailApiResponse.setData(detailResponse);

        when(submissionRepository.findByTaskName(TASK_NAME)).thenReturn(Optional.of(mockSubmission));
        when(approvalClient.detail(TASK_NAME)).thenReturn(apiResponse).thenReturn(detailApiResponse);

        // When
        ReflectionTestUtils.invokeMethod(approvalService, "updateCurrentReviewer", TASK_NAME, ACTION, REASON);

        // Then
        verify(submissionRepository).findByTaskName(TASK_NAME);
        verify(approvalClient, times(2)).detail(TASK_NAME); // Called twice: once in updateSubmissionStatusByAction, once in updateSubmissionWithCurrentReviewer
        verify(submissionRepository).save(mockSubmission);

        // Verify that the submission status was updated to "Waiting for Approval"
        assert mockSubmission.getStatus().equals("Waiting for Approval");
        assert mockSubmission.getRemarks().equals(REASON);
    }

    @Test
    @DisplayName("Should handle null detail response gracefully")
    void testUpdateCurrentReviewer_NullDetailResponse() throws Exception {
        // Given
        when(submissionRepository.findByTaskName(TASK_NAME)).thenReturn(Optional.of(mockSubmission));
        when(approvalClient.detail(TASK_NAME)).thenReturn(null);

        // When
        ReflectionTestUtils.invokeMethod(approvalService, "updateCurrentReviewer", TASK_NAME, ACTION, REASON);

        // Then
        verify(submissionRepository).findByTaskName(TASK_NAME);
        verify(approvalClient, times(2)).detail(TASK_NAME); // Called twice due to fallback to normal flow
        verify(submissionRepository).save(mockSubmission);
    }

    @Test
    @DisplayName("Should handle submission not found")
    void testUpdateCurrentReviewer_SubmissionNotFound() throws Exception {
        // Given
        when(submissionRepository.findByTaskName(TASK_NAME)).thenReturn(Optional.empty());

        // When
        ReflectionTestUtils.invokeMethod(approvalService, "updateCurrentReviewer", TASK_NAME, ACTION, REASON);

        // Then
        verify(submissionRepository).findByTaskName(TASK_NAME);
        verify(approvalClient, never()).detail(any());
        verify(submissionRepository, never()).save(any());
    }

    @Test
    @DisplayName("Should set status to 'Pending' immediately when action API is successful")
    void testSetSubmissionStatusToPending() throws Exception {
        // Given
        mockSubmission.setStatus("Waiting for Approval"); // Initial status
        when(submissionRepository.findByTaskName(TASK_NAME)).thenReturn(Optional.of(mockSubmission));

        // When
        ReflectionTestUtils.invokeMethod(approvalService, "setSubmissionStatusToPending", TASK_NAME, REASON);

        // Then
        verify(submissionRepository).findByTaskName(TASK_NAME);
        verify(submissionRepository).save(mockSubmission);

        // Verify that the submission status was updated to "Pending"
        assert mockSubmission.getStatus().equals("Pending");
        assert mockSubmission.getRemarks().equals(REASON);
    }

    @Test
    @DisplayName("Should handle submission not found when setting to Pending")
    void testSetSubmissionStatusToPending_SubmissionNotFound() throws Exception {
        // Given
        when(submissionRepository.findByTaskName(TASK_NAME)).thenReturn(Optional.empty());

        // When
        ReflectionTestUtils.invokeMethod(approvalService, "setSubmissionStatusToPending", TASK_NAME, REASON);

        // Then
        verify(submissionRepository).findByTaskName(TASK_NAME);
        verify(submissionRepository, never()).save(any());
    }

    @Test
    @DisplayName("Should update database fields when status is 'Need Revision'")
    void testUpdateTasksWithOptionalFields_NeedRevisionStatus() throws Exception {
        // Given
        String taskName1 = "TASK_001";
        String taskName2 = "TASK_002";
        List<String> taskNames = Arrays.asList(taskName1, taskName2);

        Submission submission1 = new Submission();
        submission1.setId(1L);
        submission1.setTaskName(taskName1);
        submission1.setStatus("Need Revision");
        submission1.setMonthOfProcess("January");
        submission1.setYearOfProcess("2024");
        submission1.setPaymentType("Bonus Staggered");
        submission1.setAmount(new BigDecimal("1000000"));

        Submission submission2 = new Submission();
        submission2.setId(2L);
        submission2.setTaskName(taskName2);
        submission2.setStatus("Need Revision");
        submission2.setMonthOfProcess("February");
        submission2.setYearOfProcess("2024");
        submission2.setPaymentType("Salary Adjustment");
        submission2.setAmount(new BigDecimal("2000000"));

        when(submissionRepository.findByTaskName(taskName1)).thenReturn(Optional.of(submission1));
        when(submissionRepository.findByTaskName(taskName2)).thenReturn(Optional.of(submission2));

        // When
        ReflectionTestUtils.invokeMethod(approvalService, "updateTasksWithOptionalFields",
            taskNames, "March", "2025", "Performance Staggered", new BigDecimal("3000000"));

        // Then
        verify(submissionRepository).findByTaskName(taskName1);
        verify(submissionRepository).findByTaskName(taskName2);
        verify(submissionRepository).saveAll(Arrays.asList(submission1, submission2));

        // Verify fields were updated
        assert submission1.getMonthOfProcess().equals("March");
        assert submission1.getYearOfProcess().equals("2025");
        assert submission1.getPaymentType().equals("Performance Staggered");
        assert submission1.getAmount().equals(new BigDecimal("3000000"));

        assert submission2.getMonthOfProcess().equals("March");
        assert submission2.getYearOfProcess().equals("2025");
        assert submission2.getPaymentType().equals("Performance Staggered");
        assert submission2.getAmount().equals(new BigDecimal("3000000"));
    }

    @Test
    @DisplayName("Should skip updating fields when status is not 'Need Revision'")
    void testUpdateTasksWithOptionalFields_NonNeedRevisionStatus() throws Exception {
        // Given
        String taskName = "TASK_001";
        List<String> taskNames = Arrays.asList(taskName);

        Submission submission = new Submission();
        submission.setId(1L);
        submission.setTaskName(taskName);
        submission.setStatus("Waiting for Approval"); // Not "Need Revision"
        submission.setMonthOfProcess("January");
        submission.setYearOfProcess("2024");
        submission.setPaymentType("Bonus Staggered");
        submission.setAmount(new BigDecimal("1000000"));

        when(submissionRepository.findByTaskName(taskName)).thenReturn(Optional.of(submission));

        // When
        ReflectionTestUtils.invokeMethod(approvalService, "updateTasksWithOptionalFields",
            taskNames, "March", "2025", "Performance Staggered", new BigDecimal("3000000"));

        // Then
        verify(submissionRepository).findByTaskName(taskName);
        verify(submissionRepository, never()).saveAll(any());

        // Verify fields were NOT updated
        assert submission.getMonthOfProcess().equals("January");
        assert submission.getYearOfProcess().equals("2024");
        assert submission.getPaymentType().equals("Bonus Staggered");
        assert submission.getAmount().equals(new BigDecimal("1000000"));
    }

    @Test
    @DisplayName("Should update only provided fields when status is 'Need Revision'")
    void testUpdateTasksWithOptionalFields_PartialUpdate() throws Exception {
        // Given
        String taskName = "TASK_001";
        List<String> taskNames = Arrays.asList(taskName);

        Submission submission = new Submission();
        submission.setId(1L);
        submission.setTaskName(taskName);
        submission.setStatus("Need Revision");
        submission.setMonthOfProcess("January");
        submission.setYearOfProcess("2024");
        submission.setPaymentType("Bonus Staggered");
        submission.setAmount(new BigDecimal("1000000"));

        when(submissionRepository.findByTaskName(taskName)).thenReturn(Optional.of(submission));

        // When - Only update monthOfProcess and amount
        ReflectionTestUtils.invokeMethod(approvalService, "updateTasksWithOptionalFields",
            taskNames, "March", null, null, new BigDecimal("3000000"));

        // Then
        verify(submissionRepository).findByTaskName(taskName);
        verify(submissionRepository).saveAll(Arrays.asList(submission));

        // Verify only specified fields were updated
        assert submission.getMonthOfProcess().equals("March");
        assert submission.getYearOfProcess().equals("2024"); // Unchanged
        assert submission.getPaymentType().equals("Bonus Staggered"); // Unchanged
        assert submission.getAmount().equals(new BigDecimal("3000000"));
    }

    @Test
    @DisplayName("Should handle submission not found when updating fields")
    void testUpdateTasksWithOptionalFields_SubmissionNotFound() throws Exception {
        // Given
        String taskName = "TASK_001";
        List<String> taskNames = Arrays.asList(taskName);

        when(submissionRepository.findByTaskName(taskName)).thenReturn(Optional.empty());

        // When
        ReflectionTestUtils.invokeMethod(approvalService, "updateTasksWithOptionalFields",
            taskNames, "March", "2025", "Performance Staggered", new BigDecimal("3000000"));

        // Then
        verify(submissionRepository).findByTaskName(taskName);
        verify(submissionRepository, never()).saveAll(any());
    }
}
