server.port = 8093
storage.gcs.endpoint=https://storage.googleapis.com
storage.gcs.bucket=test-arjuna-gke
storage.gcs.key.access=XVJLM5c72tH40OcQVQlp
storage.gcs.key.secret=lgOs2wLAREHYvEZdmgxVMqxscmWA9FYBBCCFO6YU
storage.gcs.url.expiry=900

# H2 Database Configuration (in-memory)
#spring.h2.console.enabled=true
#spring.datasource.url=jdbc:h2:mem:dev-staggered-payment
#spring.datasource.driverClassName=org.h2.Driver
#spring.datasource.username=dev
#spring.datasource.password=
#spring.jpa.database-platform=org.hibernate.dialect.H2Dialect

# MySQL/MariaDB Configuration (commented out by default)
# Uncomment these properties and comment out the H2 properties above to use MySQL/MariaDB
spring.datasource.url=*****************************************************
spring.datasource.username=arjuna
spring.datasource.password=Bintaro1!
spring.datasource.driver-class-name=com.mysql.cj.jdbc.Driver
spring.jpa.properties.hibernate.dialect=org.hibernate.dialect.MySQL8Dialect

# Common JPA Properties
spring.jpa.hibernate.ddl-auto=none
spring.jpa.show-sql=true
spring.jpa.properties.hibernate.format_sql=true
spring.jpa.properties.hibernate.enable_lazy_load_no_trans=true
spring.jpa.properties.hibernate.order_by.default_null_ordering=last

# To switch between databases, comment/uncomment the appropriate sections above

spring.servlet.multipart.max-file-size=3MB
spring.servlet.multipart.max-request-size=15MB
spring.servlet.multipart.enabled=true

logging.level.com.dwdo.hotdesk=info
logging.level.ROOT=info

jasypt.encryptor.password=opasquad
jhipster.security.authentication.jwt.base64-secret=ZWMzMTk2YmVkNDNmNGIyOWI3MzUwMzQxOWNjMjIxYTI1NGNmYTA1NTA0ZGFjMjlkYzhiZjVlMzNjOWM5NGI1NTc3ZGIzMDBjMGQzNDcwYjNjN2JiNDUzODVkZGQ4YTA1M2RkMTc2NDQxMjk0OTAwYTFhMmVjNGMwYjA3ZTlkMjg=
jhipster.security.cryptography.key=ENC(VVilvlrbDnoLV6F2Xi6C9H5uafKH3zbL1SiRiExxEYylpIUgeMa4Lv0vVsVTuF+AgJ7qQXnDA4g=)


feign.approval=http://localhost:8090
feign.execution=http://localhost:8095
feign.employee=http://localhost:8089

logic.code.staggered-payment-validation=STAGGERED_PAYMENT_VALIDATION
rule.code.staggered-payment=staggered_workflow
system.code.staggered-payment=arjuna
process.name.staggered-payment=Staggered Payment Approval
